<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;
class NotificationController extends Controller
{
public function index()
{
    if (Auth::guard('giang_viens')->check()) {
        $user = Auth::guard('giang_viens')->user();

        switch ($user->chucVuObj->maChucVu) {
            case 'GV':
                $notifications = Notification::where('loai', 'lich')
                    ->where('doiTuong', 'giang_vien')
                    ->latest()->get();
                break;

            case 'TBM':
            case 'TK':
                $notifications = Notification::whereIn('loai', ['xac_nhan_phieu', 'xac_nhan_bien_ban'])
                    ->latest()->get();
                break;

            default:
                $notifications = collect();
                break;
        }
    } elseif (Auth::guard('nhan_vien_p_d_b_c_ls')->check()) {
        $notifications = Notification::whereIn('loai', ['lich','bien_ban', 'phieu_dang_ky'])
        ->where('doiTuong', 'nhan_vien')    
        ->latest()->get();
    } else {
        $notifications = collect();
    }


    // return response()->json($notifications);
    return response()->json(
    $notifications->map(function ($item) {
        return [
            'id' => $item->id,
            'noiDung' => $item->noiDung,
            'daDoc' => $item->daDoc,
            'link' => $item->link ?? '#',
            'created_at' => $item->created_at->format('H:i d/m/Y'), // thời gian định dạng đẹp
        ];
    })
);
}

public function markAllAsRead()
{
    if (auth()->guard('giang_viens')->check()) {
        $user = auth()->guard('giang_viens')->user();

        switch ($user->chucVuObj->maChucVu) {
            case 'GV':
                Notification::where('loai', 'lich')
                    ->where('doiTuong', 'giang_vien')
                    ->update(['daDoc' => true]);
                break;

            case 'TBM':
            case 'TK':
                Notification::whereIn('loai', ['xac_nhan_phieu', 'xac_nhan_bien_ban'])
                    ->update(['daDoc' => true]);
                break;
        }
    } elseif (auth()->guard('nhan_vien_p_d_b_c_ls')->check()) {
        Notification::whereIn('loai', ['lich', 'bien_ban', 'phieu_dang_ky'])
            ->where('doiTuong', 'nhan_vien')
            ->update(['daDoc' => true]);
    }

    return response()->json(['message' => 'Đã đánh dấu đã đọc']);
}

public function delete(Request $request)
{
    Notification::where('id', $request->id)->delete();
    return response()->json(['message' => 'Đã xóa']);
}


}
