# MÔ TẢ YÊU CẦU CHỨC NĂNG HỆ THỐNG - QUẢN TRỊ VIÊN

## 1. ĐĂNG NHẬP

**<PERSON><PERSON> tả:** Quản trị viên truy cập vào hệ thống bằng tài khoản quản trị để thực hiện các chức năng quản lý hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** 
- <PERSON>ệ thống đang hoạt động
- Quản trị viên có tài khoản hợp lệ

**Hậu điều kiện:** 
- Quản trị viên đăng nhập thành công vào hệ thống
- Phiên làm việc được tạo

**Đảm bảo tối thiểu:** Hệ thống ghi lại thông tin đăng nhập

**Đ<PERSON>m bảo thành công:** Quản trị viên truy cập được giao diện quản trị

**K<PERSON>ch hoạt:** Quản trị viên truy cập trang đăng nhập

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập tài khoản và mật khẩu
2. Hệ thống xác thực thông tin đăng nhập
3. Hệ thống chuyển hướng đến trang quản trị chính
4. Hệ thống ghi log đăng nhập

## 2. ĐĂNG XUẤT

**Mô tả:** Quản trị viên kết thúc phiên làm việc và thoát khỏi hệ thống an toàn.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập vào hệ thống

**Hậu điều kiện:** 
- Phiên làm việc được kết thúc
- Quản trị viên được chuyển về trang đăng nhập

**Đảm bảo tối thiểu:** Phiên làm việc được xóa khỏi hệ thống

**Đảm bảo thành công:** Quản trị viên đăng xuất an toàn

**Kích hoạt:** Quản trị viên nhấn nút đăng xuất

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn chức năng đăng xuất
2. Hệ thống xóa phiên làm việc
3. Hệ thống chuyển hướng về trang đăng nhập
4. Hệ thống ghi log đăng xuất

## 3. QUÊN MẬT KHẨU

**Mô tả:** Quản trị viên khôi phục mật khẩu khi quên thông qua email.

**Actor:** Quản trị viên

**Tiền điều kiện:** 
- Tài khoản quản trị tồn tại trong hệ thống
- Email đã được cấu hình

**Hậu điều kiện:** Email khôi phục mật khẩu được gửi

**Đảm bảo tối thiểu:** Hệ thống ghi lại yêu cầu khôi phục

**Đảm bảo thành công:** Quản trị viên nhận được email khôi phục

**Kích hoạt:** Quản trị viên chọn "Quên mật khẩu"

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập tài khoản hoặc email
2. Hệ thống kiểm tra thông tin
3. Hệ thống tạo liên kết khôi phục
4. Hệ thống gửi email chứa liên kết khôi phục

## 4. QUẢN LÝ TÀI KHOẢN CÁ NHÂN

**Mô tả:** Quản trị viên cập nhật thông tin cá nhân và thay đổi mật khẩu.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Thông tin cá nhân được cập nhật

**Đảm bảo tối thiểu:** Dữ liệu cũ được bảo toàn nếu cập nhật thất bại

**Đảm bảo thành công:** Thông tin mới được lưu vào cơ sở dữ liệu

**Kích hoạt:** Quản trị viên truy cập trang thông tin cá nhân

**Chuỗi sự kiện chính:**
1. Quản trị viên chỉnh sửa thông tin cá nhân
2. Hệ thống xác thực dữ liệu đầu vào
3. Hệ thống cập nhật thông tin
4. Hệ thống hiển thị thông báo thành công

## 5. TÌM KIẾM, TRA CỨU HỆ THỐNG

**Mô tả:** Quản trị viên tìm kiếm thông tin trong toàn bộ hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Kết quả tìm kiếm được hiển thị

**Đảm bảo tối thiểu:** Hệ thống trả về thông báo nếu không tìm thấy

**Đảm bảo thành công:** Danh sách kết quả phù hợp được hiển thị

**Kích hoạt:** Quản trị viên nhập từ khóa tìm kiếm

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập từ khóa và chọn phạm vi tìm kiếm
2. Hệ thống thực hiện tìm kiếm trong cơ sở dữ liệu
3. Hệ thống hiển thị kết quả tìm kiếm
4. Quản trị viên có thể xem chi tiết từng kết quả

## 6. XEM LỊCH SINH HOẠT HỌC THUẬT

**Mô tả:** Quản trị viên xem toàn bộ lịch sinh hoạt học thuật trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Danh sách lịch sinh hoạt được hiển thị

**Đảm bảo tối thiểu:** Hiển thị thông báo nếu không có dữ liệu

**Đảm bảo thành công:** Toàn bộ lịch sinh hoạt được hiển thị đầy đủ

**Kích hoạt:** Quản trị viên truy cập menu lịch sinh hoạt

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn xem lịch sinh hoạt học thuật
2. Hệ thống truy xuất dữ liệu từ cơ sở dữ liệu
3. Hệ thống hiển thị danh sách lịch sinh hoạt
4. Quản trị viên có thể lọc theo trạng thái, thời gian

## 7. XEM DANH SÁCH GIẢNG VIÊN

**Mô tả:** Quản trị viên xem danh sách toàn bộ giảng viên trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Danh sách giảng viên được hiển thị

**Đảm bảo tối thiểu:** Hiển thị thông báo nếu không có dữ liệu

**Đảm bảo thành công:** Toàn bộ thông tin giảng viên được hiển thị

**Kích hoạt:** Quản trị viên truy cập menu quản lý giảng viên

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn xem danh sách giảng viên
2. Hệ thống truy xuất dữ liệu giảng viên
3. Hệ thống hiển thị danh sách với thông tin chi tiết
4. Quản trị viên có thể lọc theo bộ môn, khoa, trạng thái

## 8. XEM BÁO CÁO THỐNG KÊ

**Mô tả:** Quản trị viên xem các báo cáo thống kê tổng quan về hoạt động hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Báo cáo thống kê được hiển thị

**Đảm bảo tối thiểu:** Hiển thị báo cáo cơ bản nếu dữ liệu không đầy đủ

**Đảm bảo thành công:** Báo cáo chi tiết với biểu đồ được hiển thị

**Kích hoạt:** Quản trị viên truy cập menu báo cáo thống kê

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn loại báo cáo và khoảng thời gian
2. Hệ thống tính toán dữ liệu thống kê
3. Hệ thống tạo báo cáo và biểu đồ
4. Quản trị viên có thể xuất báo cáo ra file

## 9. QUẢN LÝ NGƯỜI DÙNG

### 9.1. THÊM NGƯỜI DÙNG

**Mô tả:** Quản trị viên tạo tài khoản mới cho người dùng trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập và có quyền quản lý người dùng

**Hậu điều kiện:** Tài khoản người dùng mới được tạo

**Đảm bảo tối thiểu:** Dữ liệu không hợp lệ được từ chối

**Đảm bảo thành công:** Tài khoản mới được lưu vào cơ sở dữ liệu

**Kích hoạt:** Quản trị viên chọn "Thêm người dùng"

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập thông tin người dùng mới
2. Hệ thống kiểm tra tính hợp lệ của dữ liệu
3. Hệ thống tạo tài khoản và mật khẩu mặc định
4. Hệ thống gửi thông tin tài khoản qua email

### 9.2. SỬA NGƯỜI DÙNG

**Mô tả:** Quản trị viên chỉnh sửa thông tin tài khoản người dùng.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Tài khoản người dùng tồn tại

**Hậu điều kiện:** Thông tin người dùng được cập nhật

**Đảm bảo tối thiểu:** Dữ liệu cũ được giữ nguyên nếu cập nhật thất bại

**Đảm bảo thành công:** Thông tin mới được lưu thành công

**Kích hoạt:** Quản trị viên chọn "Sửa" từ danh sách người dùng

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn người dùng cần chỉnh sửa
2. Hệ thống hiển thị form chỉnh sửa với dữ liệu hiện tại
3. Quản trị viên cập nhật thông tin
4. Hệ thống lưu thay đổi và gửi thông báo

### 9.3. XÓA NGƯỜI DÙNG

**Mô tả:** Quản trị viên vô hiệu hóa hoặc xóa tài khoản người dùng.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Tài khoản người dùng tồn tại

**Hậu điều kiện:** Tài khoản người dùng bị vô hiệu hóa

**Đảm bảo tối thiểu:** Dữ liệu liên quan được bảo toàn

**Đảm bảo thành công:** Tài khoản không thể đăng nhập

**Kích hoạt:** Quản trị viên chọn "Xóa" từ danh sách người dùng

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn người dùng cần xóa
2. Hệ thống hiển thị xác nhận xóa
3. Quản trị viên xác nhận thao tác
4. Hệ thống vô hiệu hóa tài khoản

### 9.4. IMPORT DANH SÁCH GIẢNG VIÊN

**Mô tả:** Quản trị viên nhập danh sách giảng viên từ file để tạo tài khoản hàng loạt.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- File import có định dạng hợp lệ

**Hậu điều kiện:** Danh sách tài khoản được tạo

**Đảm bảo tối thiểu:** Dữ liệu lỗi được báo cáo

**Đảm bảo thành công:** Tất cả tài khoản hợp lệ được tạo

**Kích hoạt:** Quản trị viên chọn "Import danh sách"

**Chuỗi sự kiện chính:**
1. Quản trị viên upload file danh sách
2. Hệ thống kiểm tra định dạng file
3. Hệ thống xử lý từng dòng dữ liệu
4. Hệ thống tạo báo cáo kết quả import

### 9.5. LƯU LOG HOẠT ĐỘNG

**Mô tả:** Hệ thống ghi lại toàn bộ hoạt động của quản trị viên.

**Actor:** Hệ thống

**Tiền điều kiện:** Quản trị viên thực hiện thao tác

**Hậu điều kiện:** Log được ghi vào cơ sở dữ liệu

**Đảm bảo tối thiểu:** Log cơ bản được ghi

**Đảm bảo thành công:** Log chi tiết được lưu trữ

**Kích hoạt:** Mọi thao tác của quản trị viên

**Chuỗi sự kiện chính:**
1. Quản trị viên thực hiện thao tác
2. Hệ thống ghi lại thông tin thao tác
3. Hệ thống lưu log vào cơ sở dữ liệu
4. Hệ thống đánh dấu thời gian

## 10. QUẢN LÝ BỘ MÔN

### 10.1. THÊM BỘ MÔN

**Mô tả:** Quản trị viên tạo bộ môn mới trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Bộ môn mới được tạo

**Đảm bảo tối thiểu:** Tên bộ môn không được trùng

**Đảm bảo thành công:** Bộ môn được lưu vào cơ sở dữ liệu

**Kích hoạt:** Quản trị viên chọn "Thêm bộ môn"

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập thông tin bộ môn
2. Hệ thống kiểm tra tính duy nhất của tên
3. Hệ thống lưu thông tin bộ môn
4. Hệ thống hiển thị thông báo thành công

### 10.2. SỬA BỘ MÔN

**Mô tả:** Quản trị viên chỉnh sửa thông tin bộ môn.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Bộ môn tồn tại trong hệ thống

**Hậu điều kiện:** Thông tin bộ môn được cập nhật

**Đảm bảo tối thiểu:** Dữ liệu cũ được bảo toàn nếu cập nhật thất bại

**Đảm bảo thành công:** Thông tin mới được lưu thành công

**Kích hoạt:** Quản trị viên chọn "Sửa" từ danh sách bộ môn

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn bộ môn cần chỉnh sửa
2. Hệ thống hiển thị form chỉnh sửa
3. Quản trị viên cập nhật thông tin
4. Hệ thống lưu thay đổi

### 10.3. XÓA BỘ MÔN

**Mô tả:** Quản trị viên xóa bộ môn không còn hoạt động.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Bộ môn không có giảng viên nào

**Hậu điều kiện:** Bộ môn bị xóa khỏi hệ thống

**Đảm bảo tối thiểu:** Kiểm tra ràng buộc dữ liệu

**Đảm bảo thành công:** Bộ môn được xóa an toàn

**Kích hoạt:** Quản trị viên chọn "Xóa" từ danh sách bộ môn

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn bộ môn cần xóa
2. Hệ thống kiểm tra ràng buộc dữ liệu
3. Hệ thống hiển thị xác nhận xóa
4. Quản trị viên xác nhận và hệ thống xóa bộ môn

## 11. QUẢN LÝ KHOA

### 11.1. THÊM KHOA

**Mô tả:** Quản trị viên tạo khoa mới trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Khoa mới được tạo

**Đảm bảo tối thiểu:** Tên khoa không được trùng

**Đảm bảo thành công:** Khoa được lưu vào cơ sở dữ liệu

**Kích hoạt:** Quản trị viên chọn "Thêm khoa"

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập thông tin khoa
2. Hệ thống kiểm tra tính duy nhất của tên
3. Hệ thống lưu thông tin khoa
4. Hệ thống hiển thị thông báo thành công

### 11.2. SỬA KHOA

**Mô tả:** Quản trị viên chỉnh sửa thông tin khoa.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Khoa tồn tại trong hệ thống

**Hậu điều kiện:** Thông tin khoa được cập nhật

**Đảm bảo tối thiểu:** Dữ liệu cũ được bảo toàn nếu cập nhật thất bại

**Đảm bảo thành công:** Thông tin mới được lưu thành công

**Kích hoạt:** Quản trị viên chọn "Sửa" từ danh sách khoa

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn khoa cần chỉnh sửa
2. Hệ thống hiển thị form chỉnh sửa
3. Quản trị viên cập nhật thông tin
4. Hệ thống lưu thay đổi

### 11.3. XÓA KHOA

**Mô tả:** Quản trị viên xóa khoa không còn hoạt động.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Khoa không có bộ môn nào

**Hậu điều kiện:** Khoa bị xóa khỏi hệ thống

**Đảm bảo tối thiểu:** Kiểm tra ràng buộc dữ liệu

**Đảm bảo thành công:** Khoa được xóa an toàn

**Kích hoạt:** Quản trị viên chọn "Xóa" từ danh sách khoa

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn khoa cần xóa
2. Hệ thống kiểm tra ràng buộc dữ liệu
3. Hệ thống hiển thị xác nhận xóa
4. Quản trị viên xác nhận và hệ thống xóa khoa

## 12. QUẢN LÝ QUYỀN

### 12.1. THÊM QUYỀN

**Mô tả:** Quản trị viên tạo quyền mới trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Quyền mới được tạo

**Đảm bảo tối thiểu:** Tên quyền không được trùng

**Đảm bảo thành công:** Quyền được lưu vào cơ sở dữ liệu

**Kích hoạt:** Quản trị viên chọn "Thêm quyền"

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập thông tin quyền
2. Hệ thống kiểm tra tính duy nhất của tên
3. Hệ thống lưu thông tin quyền
4. Hệ thống hiển thị thông báo thành công

### 12.2. SỬA QUYỀN

**Mô tả:** Quản trị viên chỉnh sửa thông tin quyền.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Quyền tồn tại trong hệ thống

**Hậu điều kiện:** Thông tin quyền được cập nhật

**Đảm bảo tối thiểu:** Dữ liệu cũ được bảo toàn nếu cập nhật thất bại

**Đảm bảo thành công:** Thông tin mới được lưu thành công

**Kích hoạt:** Quản trị viên chọn "Sửa" từ danh sách quyền

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn quyền cần chỉnh sửa
2. Hệ thống hiển thị form chỉnh sửa
3. Quản trị viên cập nhật thông tin
4. Hệ thống lưu thay đổi

### 12.3. XÓA QUYỀN

**Mô tả:** Quản trị viên xóa quyền không còn sử dụng.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Quyền không được gán cho người dùng nào

**Hậu điều kiện:** Quyền bị xóa khỏi hệ thống

**Đảm bảo tối thiểu:** Kiểm tra ràng buộc dữ liệu

**Đảm bảo thành công:** Quyền được xóa an toàn

**Kích hoạt:** Quản trị viên chọn "Xóa" từ danh sách quyền

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn quyền cần xóa
2. Hệ thống kiểm tra ràng buộc dữ liệu
3. Hệ thống hiển thị xác nhận xóa
4. Quản trị viên xác nhận và hệ thống xóa quyền

## 13. QUẢN LÝ CHỨC VỤ

### 13.1. THÊM CHỨC VỤ

**Mô tả:** Quản trị viên tạo chức vụ mới trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Chức vụ mới được tạo

**Đảm bảo tối thiểu:** Tên chức vụ không được trùng

**Đảm bảo thành công:** Chức vụ được lưu vào cơ sở dữ liệu

**Kích hoạt:** Quản trị viên chọn "Thêm chức vụ"

**Chuỗi sự kiện chính:**
1. Quản trị viên nhập thông tin chức vụ
2. Hệ thống kiểm tra tính duy nhất của tên
3. Hệ thống lưu thông tin chức vụ
4. Hệ thống hiển thị thông báo thành công

### 13.2. SỬA CHỨC VỤ

**Mô tả:** Quản trị viên chỉnh sửa thông tin chức vụ.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Chức vụ tồn tại trong hệ thống

**Hậu điều kiện:** Thông tin chức vụ được cập nhật

**Đảm bảo tối thiểu:** Dữ liệu cũ được bảo toàn nếu cập nhật thất bại

**Đảm bảo thành công:** Thông tin mới được lưu thành công

**Kích hoạt:** Quản trị viên chọn "Sửa" từ danh sách chức vụ

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn chức vụ cần chỉnh sửa
2. Hệ thống hiển thị form chỉnh sửa
3. Quản trị viên cập nhật thông tin
4. Hệ thống lưu thay đổi

### 13.3. XÓA CHỨC VỤ

**Mô tả:** Quản trị viên xóa chức vụ không còn sử dụng.

**Actor:** Quản trị viên

**Tiền điều kiện:**
- Quản trị viên đã đăng nhập
- Chức vụ không được gán cho người dùng nào

**Hậu điều kiện:** Chức vụ bị xóa khỏi hệ thống

**Đảm bảo tối thiểu:** Kiểm tra ràng buộc dữ liệu

**Đảm bảo thành công:** Chức vụ được xóa an toàn

**Kích hoạt:** Quản trị viên chọn "Xóa" từ danh sách chức vụ

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn chức vụ cần xóa
2. Hệ thống kiểm tra ràng buộc dữ liệu
3. Hệ thống hiển thị xác nhận xóa
4. Quản trị viên xác nhận và hệ thống xóa chức vụ

## 14. XEM DANH SÁCH BÁO CÁO

**Mô tả:** Quản trị viên xem toàn bộ báo cáo sinh hoạt học thuật trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Danh sách báo cáo được hiển thị

**Đảm bảo tối thiểu:** Hiển thị thông báo nếu không có dữ liệu

**Đảm bảo thành công:** Toàn bộ báo cáo được hiển thị với thông tin chi tiết

**Kích hoạt:** Quản trị viên truy cập menu báo cáo

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn xem danh sách báo cáo
2. Hệ thống truy xuất dữ liệu báo cáo
3. Hệ thống hiển thị danh sách với thông tin người nộp, ngày nộp, trạng thái
4. Quản trị viên có thể lọc theo người nộp, ngày tháng, trạng thái

## 15. DANH SÁCH PHIẾU ĐĂNG KÝ SHHT

**Mô tả:** Quản trị viên xem toàn bộ phiếu đăng ký sinh hoạt học thuật trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Danh sách phiếu đăng ký được hiển thị

**Đảm bảo tối thiểu:** Hiển thị thông báo nếu không có dữ liệu

**Đảm bảo thành công:** Toàn bộ phiếu đăng ký được hiển thị với trạng thái

**Kích hoạt:** Quản trị viên truy cập menu phiếu đăng ký

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn xem danh sách phiếu đăng ký
2. Hệ thống truy xuất dữ liệu phiếu đăng ký
3. Hệ thống hiển thị danh sách với trạng thái duyệt
4. Quản trị viên có thể lọc theo trạng thái, người gửi, thời gian

## 16. DANH SÁCH BIÊN BẢN

**Mô tả:** Quản trị viên xem toàn bộ biên bản sinh hoạt học thuật trong hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập

**Hậu điều kiện:** Danh sách biên bản được hiển thị

**Đảm bảo tối thiểu:** Hiển thị thông báo nếu không có dữ liệu

**Đảm bảo thành công:** Toàn bộ biên bản được hiển thị với trạng thái xác nhận

**Kích hoạt:** Quản trị viên truy cập menu biên bản

**Chuỗi sự kiện chính:**
1. Quản trị viên chọn xem danh sách biên bản
2. Hệ thống truy xuất dữ liệu biên bản
3. Hệ thống hiển thị danh sách với trạng thái xác nhận
4. Quản trị viên có thể lọc theo trạng thái, người tạo, thời gian

## 17. CẤU HÌNH EMAIL HỆ THỐNG

**Mô tả:** Quản trị viên cấu hình các thông số email và template thông báo của hệ thống.

**Actor:** Quản trị viên

**Tiền điều kiện:** Quản trị viên đã đăng nhập với quyền cấu hình hệ thống

**Hậu điều kiện:** Cấu hình email được cập nhật

**Đảm bảo tối thiểu:** Cấu hình cũ được bảo toàn nếu cập nhật thất bại

**Đảm bảo thành công:** Hệ thống email hoạt động với cấu hình mới

**Kích hoạt:** Quản trị viên truy cập menu cấu hình email

**Chuỗi sự kiện chính:**
1. Quản trị viên truy cập trang cấu hình email
2. Quản trị viên cập nhật thông số email server và template
3. Hệ thống kiểm tra tính hợp lệ của cấu hình
4. Hệ thống lưu cấu hình và test gửi email thử nghiệm

---

**Ghi chú:** Tài liệu này mô tả đầy đủ các yêu cầu chức năng hệ thống dành cho Quản trị viên, bao gồm cả chức năng kế thừa từ người dùng chung và các chức năng quản lý hệ thống đặc thù. Mỗi chức năng được mô tả chi tiết theo format chuẩn với các thành phần: Mô tả, Actor, Tiền điều kiện, Hậu điều kiện, Đảm bảo tối thiểu, Đảm bảo thành công, Kích hoạt và Chuỗi sự kiện chính.
