# BẢNG YÊU CẦU CHỨC NĂNG HỆ THỐNG - QUẢN TRỊ VIÊN

## Bảng 3.6. Chức năng hệ thống của người dùng là quản trị viên

| STT | Chức năng người dùng | <PERSON><PERSON> tả chức năng |
|-----|---------------------|-----------------|
| **Chức năng kế thừa từ người dùng chung** |
| 1 | Đăng nhập | Quản trị viên sử dụng tài khoản và mật khẩu quản trị để truy cập vào hệ thống quản lý báo cáo sinh hoạt học thuật với quyền hạn cao nhất. |
| 2 | Đăng xuất | Quản trị viên kết thúc phiên làm việc và thoát khỏi hệ thống một cách an toàn khi hoàn thành công việc quản trị. |
| 3 | Quên mật khẩu | Quản trị viên cung cấp tài khoản quản trị hoặc email để hệ thống gửi liên kết khôi phục mật khẩu qua email. |
| 4 | Quản lý tài khoản | • **Cập nhật thông tin**: Quản trị viên cập nhật các thông tin cá nhân như họ tên, email, số điện thoại.<br>• **Thay đổi mật khẩu**: Quản trị viên có thể đổi mật khẩu để đảm bảo bảo mật tài khoản quản trị. |
| 5 | Tìm kiếm, tra cứu hệ thống | Quản trị viên có thể tìm kiếm hoặc tra cứu mọi thông tin trong hệ thống như người dùng, báo cáo, biên bản, phiếu đăng ký theo từ khóa hoặc các tiêu chí khác. |
| 6 | Xem lịch sinh hoạt học thuật | Quản trị viên xem toàn bộ lịch sinh hoạt học thuật trong hệ thống, bao gồm cả lịch đã phê duyệt và đang chờ duyệt. |
| 7 | Xem danh sách giảng viên | Quản trị viên có thể xem danh sách toàn bộ giảng viên trong hệ thống với đầy đủ thông tin chi tiết. |
| 8 | Xem báo cáo thống kê | Quản trị viên xem các báo cáo thống kê tổng quan và chi tiết về toàn bộ hoạt động của hệ thống, bao gồm số liệu về người dùng, hoạt động sinh hoạt học thuật. |
| **Chức năng đặc thù của Quản trị viên** |
| 9 | Quản lý người dùng | • **Thêm người dùng**: Quản trị viên tạo tài khoản mới cho giảng viên, nhân viên PDBCL hoặc quản trị viên khác.<br>• **Sửa người dùng**: Quản trị viên chỉnh sửa thông tin tài khoản của người dùng trong hệ thống.<br>• **Xóa người dùng**: Quản trị viên vô hiệu hóa hoặc xóa tài khoản người dùng không còn hoạt động.<br>• **Import danh sách giảng viên**: Quản trị viên nhập danh sách giảng viên từ file Excel hoặc CSV để tạo tài khoản hàng loạt.<br>• **Lưu log hoạt động**: Hệ thống ghi lại toàn bộ hoạt động của quản trị viên để kiểm tra và audit. |
| 10 | Quản lý bộ môn | • **Thêm bộ môn**: Quản trị viên tạo bộ môn mới trong hệ thống với thông tin tên bộ môn, mô tả.<br>• **Sửa bộ môn**: Quản trị viên chỉnh sửa thông tin của bộ môn đã có trong hệ thống.<br>• **Xóa bộ môn**: Quản trị viên xóa bộ môn không còn hoạt động (chỉ khi không có giảng viên nào thuộc bộ môn đó). |
| 11 | Quản lý khoa | • **Thêm khoa**: Quản trị viên tạo khoa mới trong hệ thống với thông tin tên khoa, mô tả.<br>• **Sửa khoa**: Quản trị viên chỉnh sửa thông tin của khoa đã có trong hệ thống.<br>• **Xóa khoa**: Quản trị viên xóa khoa không còn hoạt động (chỉ khi không có bộ môn nào thuộc khoa đó). |
| 12 | Quản lý quyền | • **Thêm quyền**: Quản trị viên tạo các quyền mới trong hệ thống để phân quyền cho người dùng.<br>• **Sửa quyền**: Quản trị viên chỉnh sửa mô tả hoặc phạm vi của các quyền đã có.<br>• **Xóa quyền**: Quản trị viên xóa các quyền không còn sử dụng trong hệ thống. |
| 13 | Quản lý chức vụ | • **Thêm chức vụ**: Quản trị viên tạo chức vụ mới như Trưởng bộ môn, Phó trưởng bộ môn, Giảng viên.<br>• **Sửa chức vụ**: Quản trị viên chỉnh sửa thông tin mô tả của chức vụ.<br>• **Xóa chức vụ**: Quản trị viên xóa chức vụ không còn sử dụng trong hệ thống. |
| 14 | Xem danh sách báo cáo | Quản trị viên có thể xem toàn bộ báo cáo sinh hoạt học thuật đã được nộp trong hệ thống, bao gồm thông tin người nộp, ngày nộp, trạng thái. |
| 15 | Danh sách phiếu đăng ký SHHT | Quản trị viên xem toàn bộ phiếu đăng ký sinh hoạt học thuật trong hệ thống, bao gồm cả phiếu đã duyệt, chờ duyệt và bị từ chối. |
| 16 | Danh sách biên bản | Quản trị viên xem toàn bộ biên bản sinh hoạt học thuật trong hệ thống, bao gồm biên bản đã xác nhận và chờ xác nhận. |
| 17 | Cấu hình email hệ thống | Quản trị viên cấu hình các thông số email server, template email thông báo, và các thiết lập gửi email tự động của hệ thống. |
| 18 | Phân quyền | Quản trị viên được sử dụng tất cả các chức năng quản trị của hệ thống. Giảng viên chỉ được sử dụng các chức năng liên quan đến bài học, bài tập, câu hỏi, loại câu hỏi, chủ đề, thông tin làm bài của học viên. |
| 19 | Thống kê lượng truy cập của học viên | Thống kê tổng lượng truy cập, lượng truy cập theo tháng, lượng truy cập theo ngày, lượng học viên đang hoạt động trên hệ thống. |
| 20 | Thống kê số lượng | Thống kê số lượng của học viên, quản trị viên, bài học, bài tập, câu hỏi, bài viết, chủ đề, liên hệ. |
| 21 | Gửi email đến tất cả mật khẩu | Sau khi quản trị viên điền thông tin email cho chức năng "Quên mật khẩu", hệ thống sẽ tự động gửi liên kết vào địa chỉ email để viên đặt lại mật khẩu. |
| 22 | Thống kê bài viết được mua cập nhật theo thứ tự giảm dần | Thống kê các bài viết được mua cập nhật theo thứ tự giảm dần. |
