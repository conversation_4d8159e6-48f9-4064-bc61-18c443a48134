# YÊU CẦU CHỨC NĂNG HỆ THỐNG THEO NGƯỜI DÙNG
## <PERSON><PERSON> thống Quản lý Báo cáo Hoạt động Học thuật (QLBCHT)

## a. Đối với người dùng: Quản trị viên

### Bảng 3.1. <PERSON><PERSON><PERSON> năng hệ thống của người dùng là quản trị viên

| STT | Chức năng người dùng | Mô tả chức năng |
|-----|---------------------|-----------------|
| 1 | <PERSON><PERSON> quyền | • Quản trị viên được sử dụng tất cả các chức năng quản trị của hệ thống.<br>• Giảng viên chỉ được sử dụng các chức năng liên quan: xem lịch sinh ho<PERSON>t, đăng ký tham gia, nộ<PERSON> bá<PERSON>, quản lý biên bản.<br>• <PERSON><PERSON><PERSON> viên PDBCL chỉ được sử dụng các chức năng liên quan: xem lịch sinh hoạt, xác nhận phiếu đăng ký, duyệt báo cáo, quản lý biên bản. |
| 2 | Thống kê lượng truy cập của người dùng | • Thống kê tổng lượng truy cập, lượng truy cập tháng trước, lượng truy cập tháng này, lượng người dùng đang hoạt động trên hệ thống.<br>• Thống kê theo từng loại người dùng: giảng viên, nhân viên PDBCL. |
| 3 | Thống kê số lượng | Thống kê số lượng của giảng viên, nhân viên PDBCL, lịch sinh hoạt, báo cáo, biên bản, phiếu đăng ký, bộ môn, khoa, chức vụ. |
| 4 | Gửi email đặt lại mật khẩu | Sau khi quản trị viên điền thông tin email cho chức năng "Quên mật khẩu", hệ thống sẽ tự động gửi liên kết vào địa chỉ email đó để người dùng đặt lại mật khẩu. |
| 5 | Thống kê báo cáo được nộp nhiều nhất | Thống kê các chủ đề báo cáo được nộp và sử dụng nhiều nhất theo thời gian nhất định. |
| 6 | Quản lý lịch sinh hoạt học thuật | • Xem danh sách tất cả các lịch sinh hoạt trong hệ thống.<br>• Thêm, chỉnh sửa, xóa thông tin lịch sinh hoạt.<br>• Xác nhận hoặc hủy lịch sinh hoạt.<br>• Xem lịch sinh hoạt theo dạng lịch (tháng, tuần, ngày). |
| 7 | Quản lý giảng viên | • Thêm, sửa, xóa thông tin giảng viên.<br>• Phân công giảng viên vào bộ môn, khoa.<br>• Quản lý chức vụ và quyền hạn của giảng viên.<br>• Tạo và cấp tài khoản cho giảng viên mới. |
| 8 | Quản lý nhân viên PDBCL | • Thêm, sửa, xóa thông tin nhân viên PDBCL.<br>• Phân quyền cho nhân viên PDBCL.<br>• Quản lý tài khoản nhân viên PDBCL.<br>• Theo dõi hoạt động của nhân viên PDBCL. |
| 9 | Quản lý báo cáo | • Xem danh sách tất cả báo cáo trong hệ thống.<br>• Xem thông tin chi tiết và trạng thái báo cáo.<br>• Duyệt hoặc từ chối báo cáo.<br>• Xuất báo cáo thống kê theo nhiều tiêu chí. |
| 10 | Quản lý biên bản | • Xem danh sách tất cả biên bản sinh hoạt học thuật.<br>• Duyệt và xác nhận biên bản.<br>• Chỉnh sửa biên bản khi cần thiết.<br>• Xuất báo cáo biên bản theo thời gian. |
| 11 | Quản lý phiếu đăng ký | • Xem danh sách tất cả phiếu đăng ký sinh hoạt học thuật.<br>• Duyệt và xác nhận phiếu đăng ký từ trưởng bộ môn.<br>• Từ chối phiếu đăng ký với lý do cụ thể.<br>• Thống kê phiếu đăng ký theo trạng thái. |
| 12 | Quản lý bộ môn và khoa | • Thêm, sửa, xóa thông tin bộ môn và khoa.<br>• Phân công trưởng bộ môn.<br>• Quản lý danh sách giảng viên theo bộ môn.<br>• Thống kê hoạt động theo bộ môn. |
| 13 | Quản lý chức vụ | • Thêm, sửa, xóa các chức vụ trong hệ thống.<br>• Phân quyền cho từng chức vụ.<br>• Gán chức vụ cho giảng viên.<br>• Quản lý cấp bậc chức vụ. |
| 14 | Cài đặt hệ thống | • Tùy chỉnh thông tin trường (tên, địa chỉ, logo, thông tin liên hệ).<br>• Cài đặt thông số hệ thống (thời gian đăng ký, hạn nộp báo cáo).<br>• Cấu hình thông báo email và template.<br>• Quản lý các tham số cấu hình khác. |

## b. Đối với người dùng: Nhân viên PDBCL

### Bảng 3.2. Chức năng hệ thống của người dùng là nhân viên PDBCL

| STT | Chức năng người dùng | Mô tả chức năng |
|-----|---------------------|-----------------|
| 1 | Đăng nhập/Đăng xuất | • Nhân viên PDBCL đăng nhập vào hệ thống bằng tài khoản được cấp.<br>• Quản lý thông tin cá nhân và đổi mật khẩu.<br>• Đăng xuất an toàn khỏi hệ thống. |
| 2 | Xem lịch sinh hoạt học thuật | • Xem danh sách tất cả lịch sinh hoạt học thuật trong hệ thống.<br>• Lọc lịch theo bộ môn, khoa, thời gian.<br>• Xem chi tiết thông tin từng lịch sinh hoạt. |
| 3 | Xác nhận phiếu đăng ký SHHT | • Xem danh sách phiếu đăng ký từ trưởng bộ môn.<br>• Duyệt hoặc từ chối phiếu đăng ký với lý do cụ thể.<br>• Gửi thông báo kết quả duyệt đến trưởng bộ môn. |
| 4 | Quản lý biên bản | • Xem danh sách biên bản sinh hoạt học thuật.<br>• Xác nhận và duyệt biên bản từ giảng viên.<br>• Yêu cầu chỉnh sửa biên bản nếu cần thiết.<br>• Xuất báo cáo biên bản đã duyệt. |
| 5 | Xem báo cáo thống kê | • Xem thống kê hoạt động sinh hoạt học thuật theo bộ môn.<br>• Thống kê số lượng báo cáo đã nộp theo thời gian.<br>• Báo cáo tỷ lệ tham gia của giảng viên. |
| 6 | Nhận thông báo | • Nhận thông báo khi có phiếu đăng ký mới.<br>• Nhận thông báo khi có biên bản cần xác nhận.<br>• Quản lý trạng thái đọc/chưa đọc thông báo. |

## c. Đối với người dùng: Giảng viên

### Bảng 3.3. Chức năng hệ thống của người dùng là giảng viên

| STT | Chức năng người dùng | Mô tả chức năng |
|-----|---------------------|-----------------|
| 1 | Đăng nhập/Đăng xuất | • Giảng viên đăng nhập vào hệ thống bằng tài khoản được cấp.<br>• Quản lý thông tin cá nhân và đổi mật khẩu.<br>• Đăng xuất an toàn khỏi hệ thống. |
| 2 | Xem lịch sinh hoạt học thuật | • Xem danh sách lịch sinh hoạt học thuật của bộ môn.<br>• Lọc lịch theo thời gian, chủ đề, trạng thái.<br>• Xem chi tiết thông tin từng buổi sinh hoạt. |
| 3 | Đăng ký tham gia lịch | • Chọn lịch sinh hoạt muốn tham gia.<br>• Xác nhận đăng ký và nhận thông báo qua email.<br>• Kiểm tra trùng lịch tự động. |
| 4 | Quản lý đăng ký | • Xem danh sách các lịch đã đăng ký.<br>• Xem chi tiết từng đăng ký.<br>• Hủy đăng ký trong thời hạn cho phép.<br>• Xem lịch sử tham gia sinh hoạt. |
| 5 | Nộp báo cáo | • Upload file báo cáo (PDF, DOCX, PPT, PPTX).<br>• Nhập thông tin báo cáo: tên, tóm tắt, định dạng.<br>• Kiểm tra hạn nộp và xác nhận nộp báo cáo. |
| 6 | Quản lý báo cáo | • Xem danh sách báo cáo đã nộp.<br>• Tìm kiếm báo cáo theo từ khóa.<br>• Xóa báo cáo chưa được sử dụng.<br>• Tải xuống báo cáo đã nộp. |
| 7 | Quản lý biên bản | • Tạo và upload biên bản sinh hoạt học thuật.<br>• Chỉnh sửa biên bản chưa được xác nhận.<br>• Xóa biên bản không cần thiết.<br>• Xem trạng thái xác nhận biên bản. |
| 8 | Tạo lịch sinh hoạt (Trưởng bộ môn) | • Tạo lịch sinh hoạt học thuật mới cho bộ môn.<br>• Thiết lập thông tin: chủ đề, thời gian, địa điểm, hạn đăng ký.<br>• Phân công giảng viên phụ trách.<br>• Gửi thông báo đến các giảng viên trong bộ môn. |
| 9 | Chỉnh sửa lịch sinh hoạt (Trưởng bộ môn) | • Cập nhật thông tin lịch sinh hoạt đã tạo.<br>• Thay đổi thời gian, địa điểm khi cần thiết.<br>• Gia hạn thời gian đăng ký.<br>• Thông báo thay đổi đến giảng viên đã đăng ký. |
| 10 | Gửi phiếu đăng ký SHHT (Trưởng bộ môn) | • Tạo phiếu đăng ký sinh hoạt học thuật gửi PDBCL.<br>• Đính kèm thông tin chi tiết về buổi sinh hoạt.<br>• Theo dõi trạng thái duyệt phiếu đăng ký. |
| 11 | Xem thống kê cá nhân | • Xem thống kê số lần tham gia sinh hoạt học thuật.<br>• Thống kê số báo cáo đã nộp theo thời gian.<br>• Xem điểm đánh giá hoạt động học thuật. |
| 12 | Nhận thông báo | • Nhận thông báo về lịch sinh hoạt mới.<br>• Nhận nhắc nhở hạn nộp báo cáo.<br>• Nhận thông báo kết quả duyệt phiếu đăng ký.<br>• Quản lý trạng thái đọc/chưa đọc thông báo. |
